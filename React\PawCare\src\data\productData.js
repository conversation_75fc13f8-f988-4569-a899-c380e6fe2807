// Sample product data for the slider
export const newArrivals = [
  {
    id: 1,
    title: 'Dog Food',
    img: 'https://tse1.mm.bing.net/th?id=OIP.LQoGIWYDMRVonEYErln0zgHaHa&pid=Api&P=0&h=220',
    price: '$100',
    description: 'Premium dog food for all breeds'
  },
  {
    id: 2,
    title: 'Cat Food',
    img: 'https://tse1.mm.bing.net/th?id=OIP.XwXxK06ftwvUIpO82q1cdgHaHa&pid=Api&P=0&h=220',
    price: '$100',
    description: 'Nutritious cat food for healthy cats'
  },
  {
    id: 3,
    title: 'Rabbit Food',
    img: 'https://tse4.mm.bing.net/th?id=OIP.DH5jAAZsGMmdd6vySndreQHaHa&pid=Api&P=0&h=220',
    price: '$100',
    description: 'Balanced diet for rabbits'
  },
  {
    id: 4,
    title: 'Bird Food',
    img: 'https://www.richardjacksonsgarden.co.uk/wp-content/uploads/2020/09/image-27-1024x1024.png',
    price: '$100',
    description: 'Special mix for all types of birds'
  },
  {
    id: 5,
    title: 'Fish Food',
    img: 'https://tse1.mm.bing.net/th?id=OIP.vLndygwunl6m_Es3L9ZZ1AHaHa&pid=Api&P=0&h=220',
    price: '$80',
    description: 'Aquarium fish food for vibrant colors'
  },
  {
    id: 6,
    title: 'Hamster Food',
    img: 'https://tse4.mm.bing.net/th?id=OIP.OitsxXy4s0t-7YRqkfrpDwHaHa&pid=Api&P=0&h=220',
    price: '$75',
    description: 'Complete nutrition for hamsters'
  },
  {
    id: 7,
    title: 'Turtle Food',
    img: 'https://tse3.mm.bing.net/th?id=OIP.xVyR172M1gtXdDdGWx_yQgHaHa&pid=Api&P=0&h=220',
    price: '$90',
    description: 'Specialized food for turtles'
  },
  {
    id: 8,
    title: 'Puppy Food',
    img: 'https://tse4.mm.bing.net/th?id=OIP.lgD9WzoRWxj3ktzzGGF23AHaKz&pid=Api&P=0&h=220',
    price: '$120',
    description: 'Special formula for growing puppies'
  }
];

// Pet toys data
export const petToys = [
  {
    id: 101,
    title: 'Dog Chew Toy',
    img: 'https://tse1.mm.bing.net/th?id=OIP.iFEVvqOwaVy3CaAb0dEV1gHaHa&pid=Api&P=0&h=220',
    price: '$15',
    description: 'Durable chew toy for dogs'
  },
  {
    id: 102,
    title: 'Cat Teaser Wand',
    img: 'https://tse4.mm.bing.net/th?id=OIP.lp1j68jYlnKMBHZBc3KOhQHaHa&pid=Api&P=0&h=220',
    price: '$12',
    description: 'Interactive teaser wand for cats'
  },
  {
    id: 103,
    title: 'Rope Toy',
    img: 'https://tse3.mm.bing.net/th?id=OIP.Yd_dBl6YcGzE0Q9Q9lm4XAHaHa&pid=Api&P=0&h=220',
    price: '$10',
    description: 'Durable rope toy for dogs'
  },
  {
    id: 104,
    title: 'Tennis Balls',
    img: 'https://tse2.mm.bing.net/th?id=OIP.Wr-Vu8oCQXFXQMjoPpY-KQHaHa&pid=Api&P=0&h=220',
    price: '$8',
    description: 'Pack of 3 tennis balls for dogs'
  },
  {
    id: 105,
    title: 'Cat Tunnel',
    img: 'https://tse1.mm.bing.net/th?id=OIP.vLndygwunl6m_Es3L9ZZ1AHaHa&pid=Api&P=0&h=220',
    price: '$25',
    description: 'Collapsible tunnel for cats'
  },
  {
    id: 106,
    title: 'Squeaky Toy',
    img: 'https://tse4.mm.bing.net/th?id=OIP.OitsxXy4s0t-7YRqkfrpDwHaHa&pid=Api&P=0&h=220',
    price: '$7',
    description: 'Squeaky plush toy for dogs'
  },
  {
    id: 107,
    title: 'Catnip Mouse',
    img: 'https://tse3.mm.bing.net/th?id=OIP.xVyR172M1gtXdDdGWx_yQgHaHa&pid=Api&P=0&h=220',
    price: '$5',
    description: 'Catnip-filled mouse toy for cats'
  },
  {
    id: 108,
    title: 'Frisbee',
    img: 'https://tse4.mm.bing.net/th?id=OIP.lgD9WzoRWxj3ktzzGGF23AHaKz&pid=Api&P=0&h=220',
    price: '$12',
    description: 'Durable frisbee for dogs'
  }
];

// Pet accessories data
export const petAccessories = [
  {
    id: 201,
    title: 'Dog Collar',
    img: 'https://tse1.mm.bing.net/th?id=OIP.iFEVvqOwaVy3CaAb0dEV1gHaHa&pid=Api&P=0&h=220',
    price: '$20',
    description: 'Adjustable collar for dogs'
  },
  {
    id: 202,
    title: 'Cat Bed',
    img: 'https://tse4.mm.bing.net/th?id=OIP.lp1j68jYlnKMBHZBc3KOhQHaHa&pid=Api&P=0&h=220',
    price: '$35',
    description: 'Soft and cozy bed for cats'
  },
  {
    id: 203,
    title: 'Dog Leash',
    img: 'https://tse3.mm.bing.net/th?id=OIP.Yd_dBl6YcGzE0Q9Q9lm4XAHaHa&pid=Api&P=0&h=220',
    price: '$15',
    description: 'Durable leash for dogs'
  },
  {
    id: 204,
    title: 'Pet Brush',
    img: 'https://tse2.mm.bing.net/th?id=OIP.Wr-Vu8oCQXFXQMjoPpY-KQHaHa&pid=Api&P=0&h=220',
    price: '$12',
    description: 'Grooming brush for pets'
  },
  {
    id: 205,
    title: 'Cat Litter Box',
    img: 'https://tse1.mm.bing.net/th?id=OIP.vLndygwunl6m_Es3L9ZZ1AHaHa&pid=Api&P=0&h=220',
    price: '$30',
    description: 'Covered litter box for cats'
  },
  {
    id: 206,
    title: 'Dog Bowl',
    img: 'https://tse4.mm.bing.net/th?id=OIP.OitsxXy4s0t-7YRqkfrpDwHaHa&pid=Api&P=0&h=220',
    price: '$18',
    description: 'Stainless steel food bowl for dogs'
  },
  {
    id: 207,
    title: 'Cat Scratching Post',
    img: 'https://tse3.mm.bing.net/th?id=OIP.xVyR172M1gtXdDdGWx_yQgHaHa&pid=Api&P=0&h=220',
    price: '$45',
    description: 'Sisal scratching post for cats'
  },
  {
    id: 208,
    title: 'Pet Carrier',
    img: 'https://tse4.mm.bing.net/th?id=OIP.lgD9WzoRWxj3ktzzGGF23AHaKz&pid=Api&P=0&h=220',
    price: '$50',
    description: 'Travel carrier for pets'
  }
];
