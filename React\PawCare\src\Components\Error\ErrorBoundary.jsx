import React from 'react';
import { Link } from 'react-router-dom';
import { FaPaw, FaHome, FaRedo, FaExclamationTriangle } from 'react-icons/fa';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console or error reporting service
    console.error('Error caught by boundary:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  handleRefresh = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center px-4">
          <div className="max-w-lg w-full text-center">
            {/* Error Illustration */}
            <div className="mb-8">
              <div className="relative">
                <div className="bg-red-100 rounded-full w-32 h-32 mx-auto flex items-center justify-center mb-4">
                  <FaExclamationTriangle className="text-5xl text-red-500" />
                </div>
                <FaPaw className="text-2xl text-red-400 absolute top-2 right-1/3 animate-pulse" />
              </div>
            </div>

            {/* Error Message */}
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                Oops! Something Went Wrong
              </h2>
              <p className="text-gray-600 text-lg mb-2">
                Our pets seem to have knocked something over in the code!
              </p>
              <p className="text-gray-500">
                Don't worry, we're working on fixing this issue.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <button
                onClick={this.handleRefresh}
                className="flex items-center gap-2 bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors duration-300 shadow-md"
              >
                <FaRedo />
                Try Again
              </button>

              <Link
                to="/home"
                className="flex items-center gap-2 bg-white text-red-500 border-2 border-red-500 px-6 py-3 rounded-lg hover:bg-red-500 hover:text-white transition-colors duration-300 shadow-md"
              >
                <FaHome />
                Go Home
              </Link>
            </div>

            {/* Error Details (only in development) */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="mt-8 p-4 bg-gray-100 rounded-lg text-left">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Error Details (Development Only):
                </h3>
                <pre className="text-xs text-gray-600 overflow-auto max-h-40">
                  {this.state.error.toString()}
                  {this.state.errorInfo.componentStack}
                </pre>
              </div>
            )}

            {/* Support Information */}
            <div className="mt-8 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
              <p className="text-sm text-blue-800">
                <strong>Need Help?</strong> If this problem persists, please contact our support team
                or try refreshing the page.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
