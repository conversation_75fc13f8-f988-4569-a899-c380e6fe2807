
import React from 'react'
import doghero from '../../assets/imgs/doghero.png'
import { FaInstagram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import shopani from '../../assets/imgs/Shop/shopani.png';
import shopdog from '../../assets/imgs/Shop/shopdog.png';
import GenericProductSlider from '../Slider/GenericProductSlider';

const ShopView = () => {

  const shopProducts = [
    { id: 1, title: 'Dog Toy', img: 'https://tse1.mm.bing.net/th?id=OIP.iFEVvqOwaVy3CaAb0dEV1gHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog toys for your furry friend' },
    { id: 2, title: 'Dog Toy', img: shopani, price: '$100', description: 'Premium dog toys for your furry friend' },
    { id: 3, title: 'Dog Toy', img: 'https://tse4.mm.bing.net/th?id=OIP.lp1j68jYlnKMBHZBc3KOhQHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog toys for your furry friend' },
    { id: 4, title: 'Dog Toy', img: shopdog, price: '$100', description: 'Premium dog toys for your furry friend' },
    { id: 5, title: 'Dog Toy', img: 'https://tse4.mm.bing.net/th?id=OIP.Jn6EjZplFlLzxo8PeRnmpQHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog toys for your furry friend' },
    { id: 6, title: 'Dog Toy', img: 'https://tse4.mm.bing.net/th?id=OIP.uTVBGzZV9eGCzPEBfJrM2AHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog toys for your furry friend' },
    { id: 7, title: 'Dog Toy', img: 'https://tse1.mm.bing.net/th?id=OIP.9sQg5FYU89OQhm74_-jCFAHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog toys for your furry friend' },

  ];

  const FoodProducts =[
    { id: 1, title: 'Dog Food', img: 'https://tse4.mm.bing.net/th?id=OIP.6_jm7d05QMZ6vzjS_W2A0QHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog food for your furry friend' },
    { id: 2, title: 'Dog Food', img: 'https://tse2.mm.bing.net/th?id=OIP.SNF9AhIsu00AZQ9SKcxldQHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog food for your furry friend' },
    { id: 3, title: 'Dog Food', img: 'https://m.media-amazon.com/images/I/81uXeVSFHQL._AC_SL1500_.jpg', price: '$100', description: 'Premium dog food for your furry friend' },
    { id: 4, title: 'Dog Food', img: 'https://tse2.mm.bing.net/th?id=OIP.smR6zsidCfxSDuoWPDaxRgHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog food for your furry friend' },
    { id: 5, title: 'Dog Food', img: 'https://i5.walmartimages.com/asr/5dd78081-b8e1-4c06-bd3a-41c4a0d3ae97.6142d21f749105d90c756d04d820846f.jpeg' , price: '$100', description: 'Premium dog food for your furry friend' },
    { id: 6, title: 'Dog Food', img: 'https://i5.walmartimages.com/asr/4e54364b-1930-437d-8661-a341fdbec858.0a4c5d575889d053999566ea26f6e7c9.jpeg', price: '$100', description: 'Premium dog food for your furry friend' },
    { id: 7, title: 'Dog Food', img: 'http://c.shld.net/rpx/i/s/i/spin/10127449/prod_ec_1699882402??hei=64&wid=64&qlt=50', price: '$100', description: 'Premium dog food for your furry friend' },
  ]

  const Furniture = [

    { id: 1, title: 'Dog House', img: 'https://tse1.mm.bing.net/th?id=OIP.Ivl-kVtLJuclM4C_t6FdjAHaFX&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog bed for your furry friend' },
    { id: 2, title: 'Dog Bed', img: 'https://tse2.mm.bing.net/th?id=OIP.Q6X4bkemHwHllauMtW84hwHaE7&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog bed for your furry friend' },
    {id:3 , title:'Dog sofa', img:'https://tse2.mm.bing.net/th?id=OIP.pRvjH1jKeAxBIHj6wg30eAHaGW&pid=Api&P=0&h=220', price:'$100', description:'Premium dog bed for your furry friend' },
    {id:4 , title:'Dog Bath tub', img:'https://tse1.mm.bing.net/th?id=OIP.NZmyurnP38EORoi82v8WSgHaDR&pid=Api&P=0&h=220', price:'$100', description:'Premium dog bed for your furry friend' },
    {id:5 , title:'Dog Grooming Table',img:'https://tse1.mm.bing.net/th?id=OIP.iRhlbCEUHB2vrpGaExYNkAHaHa&pid=Api&P=0&h=220', price:'$100', description:'Premium dog bed for your furry friend' },
    {id:6 , title:'Dog Collar', img:'https://tse4.mm.bing.net/th?id=OIP.aIwTmEvMnMCgQmHqt2-A0AHaHa&pid=Api&P=0&h=220', price:'$100', description:'Premium dog bed for your furry friend' },
    {id:7 , title:'Dog Nail Clipper', img:'https://tse1.mm.bing.net/th?id=OIP.6dc62ko80T1_XDaSYd8rkQHaHa&pid=Api&P=0&h=220', price:'$100', description:'Premium dog bed for your furry friend' },
  ]

  const Grooming = [
    { id: 1, title: 'Dog Grooming', img:'https://i.pinimg.com/originals/9b/1e/ed/9b1eedeed36e6521d8635372fdf9eab4.jpg', price: '$100', description: 'Premium dog grooming for your furry friend' },
    { id: 2, title: 'Dog Grooming', img:'https://www.dogslife.com.au/wp-content/uploads/2014/06/rosemary-lavender-ylang-ylang-gift-box.jpg', price: '$100', description: 'Premium dog grooming for your furry friend' },
    { id: 3, title: 'Dog Grooming', img:'https://i.pinimg.com/originals/4b/83/66/4b83669ff32f02c8e4766782d39c6e44.png', price: '$100', description: 'Premium dog grooming for your furry friend' },
    { id: 4, title: 'Dog Grooming', img:'https://tse2.mm.bing.net/th?id=OIP.gdyVD6KyDCvbIkwLmb4izAHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog grooming for your furry friend' },
    { id: 5, title: 'Dog Grooming', img:'https://tse4.mm.bing.net/th?id=OIP.j909G2eR4edvXLU7LGKFoAHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog grooming for your furry friend' },
    { id: 6, title: 'Dog Grooming', img: 'https://i5.walmartimages.com/asr/3cf286a7-e975-47aa-80ea-ccc79fe5a58d.ed1d60ba4678fd80f5954342fbb203e2.jpeg', price: '$100', description: 'Premium dog grooming for your furry friend' },
    { id: 7, title: 'Dog Grooming', img: 'https://i.pinimg.com/originals/47/bc/4d/47bc4df31a21ca5593d0b316f6127c08.jpg', price: '$100', description: 'Premium dog grooming for your furry friend' },
   ]
  return (
    <>
      <section>
        <div className='p-10'>
          <div className='grid grid-cols-2 w-full  rounded-4xl p-10 relative'>
            <div className='flex justify-center'>
              <img className='h-130 object-contain' src={doghero} alt="Dog" />
            </div>

            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold'>
                culpa qui officia deserunt
              </h1>

              <div className='pt-10'>
                <p className='block text-xl font-normal mt-2'>
                  Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur Excepteur sint.
                </p>

                <div className='flex items-center gap-2 mt-4'>
                  <div className='h-px bg-gray-400 flex-grow'></div>
                  <div className='flex gap-3'>
                    <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                    <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section>
        <GenericProductSlider products={shopProducts} title="Toy Products" />
      </section>

      <section>
        <GenericProductSlider products={FoodProducts} title="Food Products" />
      </section>

      <section>
        <GenericProductSlider products={Furniture} title="Furniture Products" />
      </section>

      <section>
        <GenericProductSlider products={Grooming} title="Grooming Products" />
      </section>
    </>
  )
}

export default ShopView

