
import React from 'react'
import { Fa<PERSON>nstagram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import GenericProductSlider from '../Slider/GenericProductSlider'


const Toys = () => {

    const dogToys = [
        { id: 1, title: 'Dog Toy', img: 'https://www.pngkey.com/png/full/305-3057207_toy1-dog-toy-transparent-background.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 2, title: 'Dog Toy', img:'https://www.pngkey.com/png/full/305-3056551_2-knot-tug-o-war-rope-dog-toy.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 3, title: 'Dog Toy', img:'https://www.seekpng.com/png/full/305-3057153_dog-toy-png.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 4, title: 'Dog Toy', img:'https://wallpapers.com/images/hd/dog-toy-for-fetching-png-nhj-32k9afj73dxgr683.png', price: '$100', description: 'Premium dog toys for your furry friend' },
       { id: 5, title: 'Dog Toy', img:'https://png.pngtree.com/png-vector/20231115/ourmid/pngtree-dog-toy-white-background-dog-png-image_10592746.png', price: '$100', description: 'Premium dog toys for your furry friend' },
    ]

const catToys = [
    { id: 11, title: 'Cat Toy', img: 'https://wallpapers.com/images/hd/cat-toy-with-feathers-png-njl41-rap7w2y926xqhj9f.png', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 12, title: 'Cat Toy', img: 'https://wallpapers.com/images/hd/cat-toy-with-feathers-png-10-wa2yfq3p5tdykvqe.png', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 13, title: 'Cat Toy', img: 'https://www.pikpng.com/pngl/b/591-5913245_cat-toy-png.png', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 14, title: 'Cat Toy', img: 'https://pngpix.com/images/hd/ball-tower-cat-toy-png-68-zuggx452cqoycue1.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
    { id: 15, title: 'Cat Toy', img: 'https://wallpapers.com/images/hd/swing-ball-cat-toy-png-06212024-9wbec2wmrhke6dxb.png', price: '$100', description: 'Premium cat toys for your furry friend' },

]

const specialtyToys = [

    { id: 21, title: 'Specialty Toy', img: 'https://tse4.mm.bing.net/th?id=OIP.LlGmxmZlxV-OSINCDdoOdwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 22, title: 'Specialty Toy', img: 'https://png.pngtree.com/png-vector/20240103/ourmid/pngtree-dog-toy-on-white-plaything-png-image_11263090.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 23, title: 'Specialty Toy', img: 'https://png.pngtree.com/png-vector/20240104/ourmid/pngtree-dog-toy-chewing-png-image_11174327.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 24, title: 'Specialty Toy', img: 'https://www.pngkey.com/png/full/305-3056807_dog-toys.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
    { id: 25, title: 'Specialty Toy', img: 'https://wallpapers.com/images/hd/dog-toy-ball-png-89-nxw42hazlqkztghh.png', price: '$100', description: 'Premium specialty toys for your furry friend' },
]
  return (
    <>
      <section>
        <div className='p-10'>
          <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative'>
            <div className='flex justify-center'>
              <img className='h-130 object-contain' src='https://assets.petco.com/petco/image/upload/f_auto,q_auto:best,dpr_2.0,w_500/best-selling-cat-toys-hero' alt="toys" />
            </div>

            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold'>
                New toys for your pets
              </h1>

              <div className='pt-10'>
                <p className='block text-xl font-normal mt-2'>
                  Find the perfect toys for your furry friend. We offer a wide selection of stylish and playful toys for dogs and cats of all sizes.
                </p>

                <div className='flex items-center gap-2 mt-4'>
                  <div className='h-px bg-gray-400 flex-grow'></div>
                  <div className='flex gap-3'>
                    <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                    <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

       <section> <GenericProductSlider products={dogToys} title="Dog Toys"slidesToShow={3} autoplaySpeed={4000} /></section>

       <section> <GenericProductSlider products={catToys} title="Cat Toys"slidesToShow={3} autoplaySpeed={4000} /></section>

       <section> <GenericProductSlider products={specialtyToys} title="Specialty Toys"slidesToShow={4} autoplaySpeed={4000} /></section>
    </>
  )
}

export default Toys
