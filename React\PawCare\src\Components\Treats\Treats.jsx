import React from 'react'
import { Fa<PERSON>nstagram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import GenericProductSlider from '../Slider/GenericProductSlider'

const Treats = () => {
  // Product data for dog treats
  const dogTreats = [
    {
      id: 1,
      title: 'Dental Chews',
      img: 'https://clipground.com/images/dog-treats-png-5.png',
      price: '$14.99',
      description: 'Helps clean teeth and freshen breath'
    },
    {
      id: 2,
      title: 'Training Treats',
      img: 'https://static.vecteezy.com/system/resources/thumbnails/047/067/444/small_2x/bone-shaped-dog-biscuit-treat-free-png.png',
      price: '$9.99',
      description: 'Small treats perfect for training'
    },
    {
      id: 3,
      title: 'Jerky Treats',
      img: 'https://wallpapers.com/images/hd/senior-dog-treat-formula-png-14-oeutqviphea2fbce.png',
      price: '$16.99',
      description: 'Premium jerky treats for dogs'
    },
    {
      id: 4,
      title: 'Biscuit Treats',
      img: 'https://pngpix.com/images/hd/natural-dog-treat-selection-png-swi-zp0cr6qaokgkweqq.jpg',
      price: '$12.99',
      description: 'Crunchy biscuits dogs love'
    },
    {
      id: 5,
      title: 'Grain-Free Treats',
      img: 'https://www.poshaprani.com/wp-content/uploads/2019/08/pedigree-tasty-bites-chewy-slices-with-beef-155gm-874x1024.png',
      price: '$18.99',
      description: 'Grain-free treats for sensitive dogs'
    }
  ];

  // Product data for cat treats
  const catTreats = [
    {
      id: 101,
      title: 'Crunchy Cat Treats',
      img: 'https://i5.walmartimages.com/asr/06d0a91e-d693-4164-bf44-cd261abec9c1_4.56e485c836132dc71d72d85f6371d317.png',
      price: '$8.99',
      description: 'Crunchy treats cats love'
    },
    {
      id: 102,
      title: 'Soft Cat Treats',
      img: 'https://pacificpet.net/img/products/50089793.png',
      price: '$9.99',
      description: 'Soft and chewy treats for cats'
    },
    {
      id: 103,
      title: 'Dental Cat Treats',
      img: 'https://i5.walmartimages.com/seo/BONKERS-Purrpops-Lollipop-Cat-Treats-Chicky-Licks-Flavor-4-Pack_74201d5e-0f96-4bdb-bc5f-43a6662b6ae6.4d11317f4cca4523731f90cf0bab2060.png',
      price: '$11.99',
      description: 'Helps maintain dental health'
    },
    {
      id: 104,
      title: 'Catnip Treats',
      img: 'https://littlestinkers.ca/cdn/shop/products/FrommPurrSnackittySoft_SavoryCatTreats_85g_ChickenFlavor1.png?v=**********&width=416',
      price: '$10.99',
      description: 'Treats infused with catnip'
    },
    {
      id: 105,
      title: 'Hairball Control Treats',
      img: 'https://cdn.shopify.com/s/files/1/0054/7320/6307/products/KitCat-KitCatPurrPuree-Chicken_FiberHariballControl_CatTreat.png?v=**********',
      price: '$12.99',
      description: 'Helps reduce hairballs'
    }
  ];

  // Product data for specialty treats
  const specialtyTreats = [
    {
      id: 201,
      title: 'Calming Treats',
      img: 'https://tse1.mm.bing.net/th?id=OIP.1qeUdbRk4xfjG1GsIvXutgHaJQ&pid=Api&P=0&h=220',
      price: '$19.99',
      description: 'Helps reduce anxiety in pets'
    },
    {
      id: 202,
      title: 'Joint Health Treats',
      img: 'https://www.k9bytesgifts.com/gallery/dog-treats-2021.jpg',
      price: '$21.99',
      description: 'Supports joint health and mobility'
    },
    {
      id: 203,
      title: 'Organic Treats',
      img: 'https://roccotreats.com/wp-content/uploads/2023/05/Dog-Treats.png',
      price: '$16.99',
      description: 'Made with organic ingredients'
    },
    {
      id: 204,
      title: 'Allergy-Free Treats',
      img: 'https://i5.walmartimages.com/asr/1dc3118d-65d3-4b8e-8a6e-3eb3edc573d0.5a5d1b4067d5df30ae10896449a2610a.png',
      price: '$17.99',
      description: 'For pets with food sensitivities'
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section>
        <div className='p-10'>
          <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative'>
            <div className='flex justify-center'>
              <img className='h-130 object-contain' src='https://www.schmackos.com.au/sites/g/files/fnmzdf4436/files/2023-10/Schmackos_Rebrand%202022_Website_Assets_Pack%20Render%20Everyday%20-%20425x425px%20-%20Strapz%20Chicken.png' alt="pet treats" />
            </div>

            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold'>
                Delicious Pet Treats
              </h1>

              <div className='pt-10'>
                <p className='block text-xl font-normal mt-2'>
                  Reward your furry friends with our delicious and healthy treats. Perfect for training, rewarding, or just showing your pet some extra love.
                </p>

                <div className='flex items-center gap-2 mt-4'>
                  <div className='h-px bg-gray-400 flex-grow'></div>
                  <div className='flex gap-3'>
                    <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                    <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dog Treats Slider Section */}
      <section>
        <GenericProductSlider
          products={dogTreats}
          title="Dog Treats"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>

      {/* Cat Treats Slider Section */}
      <section>
        <GenericProductSlider
          products={catTreats}
          title="Cat Treats"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>

      {/* Specialty Treats Slider Section */}
      <section className="mb-10">
        <GenericProductSlider
          products={specialtyTreats}
          title="Specialty Treats"
          slidesToShow={4}
          autoplay={false}
          className="bg-gray-50 py-10 rounded-xl"
        />
      </section>
    </>
  )
}

export default Treats
