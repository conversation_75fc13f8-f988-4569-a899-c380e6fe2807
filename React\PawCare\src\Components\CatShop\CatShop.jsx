
import React from 'react'
import { Fa<PERSON>nstagram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import GenericProductSlider from '../Slider/GenericProductSlider'

const CatShop = () => {
    const shopProducts = [
        { id: 1, title: 'Cat Toy', img: 'https://m.media-amazon.com/images/I/61bf1yoHyKL._AC_SL1100_.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 2, title: 'Cat Toy', img: 'https://tse4.mm.bing.net/th?id=OIP.kJCBt1ZqHj5HmX1o2mFb_QHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 3, title: 'Cat Toy', img: 'https://m.media-amazon.com/images/I/81kT2raqINL.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 4, title: 'Cat Toy', img:'https://tse3.mm.bing.net/th?id=OIP.8g6Ff_L8td8hjkKYnsjDggAAAA&pid=Api&P=0&h=220', price: '$100', description: 'Premium dog toys for your furry friend' },
        { id: 5, title: 'Cat Toy', img: 'https://tse1.mm.bing.net/th?id=OIP.mfsqt6EXFOo-6woRIS859AHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 6, title: 'Cat Toy', img: 'https://m.media-amazon.com/images/I/81SyIhbxlOL._AC_SL1500_.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },
        { id: 7, title: 'Cat Toy', img: 'https://images-na.ssl-images-amazon.com/images/I/51qY3tUBtOL._AC_SL1001_.jpg', price: '$100', description: 'Premium cat toys for your furry friend' },

      ];

      const FoodProducts =[
        { id: 1, title: 'Cat Food', img: 'https://tse1.mm.bing.net/th?id=OIP.P3FMcOoc19cqDtF80-LKOgHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 2, title: 'Cat Food', img: 'https://i5.walmartimages.com/asr/2700f3bb-06f4-4aa3-b23e-cbf54775204b.4a2607976c1debf4c8efe8838aaae30f.jpeg', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 3, title: 'Cat Food', img: 'https://tse2.mm.bing.net/th?id=OIP.8Fn7qeOVf_JOQ4zijV_09QHaHa&pid=Api&P=0&h=220', description: 'Premium Cat food for your furry friend' },
        { id: 4, title: 'Cat Food', img: 'https://www.catfoodsadvisor.com/wp-content/uploads/2020/04/3-45.jpg', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 5, title: 'Cat Food', img: 'https://tse1.mm.bing.net/th?id=OIP.EpTu73wMuVt1WeEL2kEBjQHaHa&pid=Api&P=0&h=220' , price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 6, title: 'Cat Food', img: 'https://i5.walmartimages.com/asr/2be6df13-1c3f-4f06-a8f5-36f16024165a.cf9b5f482403cfd3468b9a64ac8bce6a.jpeg', price: '$100', description: 'Premium Cat food for your furry friend' },
        { id: 7, title: 'Cat Food', img: 'https://tse2.mm.bing.net/th?id=OIP.5TDhdyPZpUY4i17RQMCtBwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium Cat food for your furry friend' },
      ]

      const Furniture = [

        { id: 1, title:'Cat  House', img: 'https://tse3.mm.bing.net/th?id=OIP.dAXSehySWdmu-K1ouopIRwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat bed for your furry friend' },
        { id: 2, title:'Cat  Bed', img: 'https://images-na.ssl-images-amazon.com/images/I/618SNkdn8IL._AC_SX466_.jpg', price: '$100', description: 'Premium cat bed for your furry friend' },
        {id:3 , title:'Cat sofa', img:'https://tse1.mm.bing.net/th?id=OIP.aXhE0l4HkmmNArQSAoSvKwHaGc&pid=Api&P=0&h=220', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:4 , title:'Cat Bath tub', img:'https://tse4.mm.bing.net/th?id=OIP.POnBBzWQNcp020uQAdz1aAHaFL&pid=Api&P=0&h=220', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:5 , title:'Cat Grooming Table',img:'https://i.ebayimg.com/images/g/CJAAAOSwFytjyOGw/s-l500.jpg', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:6 , title:'Cat Collar', img:'https://tse1.mm.bing.net/th?id=OIP.g6nLaNm3XxEcqd0PWL__AgHaH1&pid=Api&P=0&h=220', price:'$100', description:'Premium cat bed for your furry friend' },
        {id:7 , title:'Cat Nail Clipper', img:'https://m.media-amazon.com/images/I/71GAKn+w23L._AC_SL1500_.jpg', price:'$100', description:'Premium cat bed for your furry friend' },
      ]

      const Grooming = [
        { id: 1, title: 'Cat Grooming', img:'https://tse2.mm.bing.net/th?id=OIP.nvfO3NBVs0fjRUTNj9q3_AHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 2, title: 'Cat Grooming', img:'https://m.media-amazon.com/images/I/71Cj4f2aj-L._AC_SX569_.jpg', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 3, title: 'Cat Grooming', img:'https://cdn-fastly.petguide.com/media/2022/03/02/8296264/best-cat-grooming-products.jpg?size=720x845&nocrop=1', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 4, title: 'Cat Grooming', img:'https://tse4.mm.bing.net/th?id=OIP.-4tdEfAmjE2p-Py0fmjOogHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 5, title: 'Cat Grooming', img:'https://tse4.mm.bing.net/th?id=OIP.bGGEDWhgGUUgbUgg8C7U3AHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 6, title: 'Cat Grooming', img:'https://tse3.mm.bing.net/th?id=OIP.pLbg8w1sCCTh9HVAtLuWCwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
        { id: 7, title: 'Cat Grooming', img:'https://tse1.mm.bing.net/th?id=OIP.WXSvp3ND1hHoPxPyftYZfwHaHa&pid=Api&P=0&h=220', price: '$100', description: 'Premium cat grooming for your furry friend' },
       ]
      return (
        <>
          <section>
            <div className='p-10'>
              <div className='grid grid-cols-2 w-full  rounded-4xl p-10 relative'>
                <div className='flex justify-center'>
                  <img className='h-130 object-contain' src='https://img.freepik.com/premium-photo/cute-ginger-cat-sitting-looking-camera-isolated-white-background_270100-2630.jpg?w=2000' alt="Cat" />
                </div>

                <div className='flex flex-col justify-center'>
                  <h1 className='text-6xl font-bold'>
                    culpa qui officia deserunt
                  </h1>

                  <div className='pt-10'>
                    <p className='block text-xl font-normal mt-2'>
                      Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur Excepteur sint.
                    </p>

                    <div className='flex items-center gap-2 mt-4'>
                      <div className='h-px bg-gray-400 flex-grow'></div>
                      <div className='flex gap-3'>
                        <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                        <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                        <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                        <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section>
            <GenericProductSlider products={shopProducts} title="Toy Products" />
          </section>

          <section>
            <GenericProductSlider products={FoodProducts} title="Food Products" />
          </section>

          <section>
            <GenericProductSlider products={Furniture} title="Furniture Products" />
          </section>

          <section>
            <GenericProductSlider products={Grooming} title="Grooming Products" />
          </section>
        </>
      )

}

export default CatShop
