import React from 'react'
import { Fa<PERSON>nstagram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import GenericProductSlider from '../Slider/GenericProductSlider'

const Furniture = () => {
  // Product data for dog furniture
  const dogFurniture = [
    {
      id: 1,
      title: 'Dog Sofa',
      img: 'https://lh3.googleusercontent.com/-OoJcSRJ_mus/Vt8TdA8-kII/AAAAAAAAFGc/-WVBkITS6CI/s576-Ic42/Bal-Grey-Grey.png',
      price: '$129.99',
      description: 'Comfortable sofa for dogs'
    },
    {
      id: 2,
      title: 'Dog Crate',
      img: 'https://i.pinimg.com/originals/b1/82/04/b1820467b47b44484fd5c51b6e004916.png',
      price: '$89.99',
      description: 'Durable wire crate for dogs'
    },
    {
      id: 3,
      title: 'Dog House',
      img: 'https://wallpapers.com/images/hd/cottage-dog-house-png-qsw-9thq3n3msl76h45k.png',
      price: '$149.99',
      description: 'Indoor/outdoor dog house'
    },
    {
      id: 4,
      title: 'Dog Steps',
      img: 'https://png.pngtree.com/png-clipart/20210309/original/pngtree-cute-brown-wooden-steps-png-image_5854874.png',
      price: '$59.99',
      description: 'Steps to help dogs reach furniture'
    },
    {
      id: 5,
      title: 'Dog Gate',
      img: 'https://png.pngtree.com/png-clipart/20231006/original/pngtree-3d-wood-fence-gate-isolated-png-image_13128494.png',
      price: '$79.99',
      description: 'Adjustable gate for doorways'
    }
  ];

  // Product data for cat furniture
  const catFurniture = [
    {
      id: 101,
      title: 'Cat Tree',
      img: 'https://png.pngtree.com/png-vector/20240326/ourmid/pngtree-cat-tree-with-cat-house-cat-tower-and-scratching-post-pets-png-image_12208615.png',
      price: '$99.99',
      description: 'Multi-level cat tree with perches'
    },
    {
      id: 102,
      title: 'Cat Condo',
      img: 'https://www.petcircle.com.au/petcircle-assets/images/products/p/paws-for-life-retro-style-cat-tree.png',
      price: '$129.99',
      description: 'Luxury condo for cats'
    },
    {
      id: 103,
      title: 'Window Perch',
      img: 'https://pngimg.com/uploads/window/window_PNG17655.png',
      price: '$39.99',
      description: 'Window-mounted perch for cats'
    },
    {
      id: 104,
      title: 'Scratching Post',
      img: 'https://png.pngtree.com/png-vector/20240706/ourmid/pngtree-playful-kitten-scratch-post-antics-png-image_13011215.png',
      price: '$49.99',
      description: 'Sisal scratching post for cats'
    },
    {
      id: 105,
      title: 'Cat Shelves',
      img: 'https://www.catsplay.com/image/cache/prod/data/img/myzoo/myzoo1/2019-01-25_08:54:49-1200x1200.png',
      price: '$69.99',
      description: 'Wall-mounted shelves for cats'
    }
  ];

  // Product data for specialty furniture
  const specialtyFurniture = [
    {
      id: 201,
      title: 'Heated Pet Furniture',
      img: 'https://www.acupet.com.au/wp-content/uploads/2019/10/P68063.png',
      price: '$89.99',
      description: 'Self-warming furniture for pets'
    },
    {
      id: 202,
      title: 'Outdoor Pet House',
      img: 'https://www.pngmart.com/files/10/Wood-Dog-House-Transparent-Background.png',
      price: '$199.99',
      description: 'Weather-resistant outdoor shelter'
    },
    {
      id: 203,
      title: 'Pet Playpen',
      img: 'https://www.myitchydog.co.uk/wp-content/uploads/2024/07/Yaheetech-8-Panel-Dog-Playpen.png',
      price: '$79.99',
      description: 'Portable playpen for pets'
    },
    {
      id: 204,
      title: 'Multi-Pet Furniture',
      img: 'https://webstockreview.net/images/clipart-bed-dog-bed-12.png',
      price: '$149.99',
      description: 'Furniture designed for multiple pets'
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section>
        <div className='p-10'>
          <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative'>
            <div className='flex justify-center'>
              <img className='h-130 object-contain' src='https://tse1.mm.bing.net/th?id=OIP.3eDgPlP-pMRJWTB37tzmlAAAAA&pid=Api&P=0&h=220https://m.media-amazon.com/images/I/71cGMXfVgdL._AC_SL1500_.jpg' alt="pet furniture" />
            </div>

            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold'>
                Pet Furniture
              </h1>

              <div className='pt-10'>
                <p className='block text-xl font-normal mt-2'>
                  Stylish and comfortable furniture for your pets. From cozy beds to climbing trees, we have everything to make your pet feel at home.
                </p>

                <div className='flex items-center gap-2 mt-4'>
                  <div className='h-px bg-gray-400 flex-grow'></div>
                  <div className='flex gap-3'>
                    <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                    <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dog Furniture Slider Section */}
      <section>
        <GenericProductSlider
          products={dogFurniture}
          title="Dog Furniture"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>

      {/* Cat Furniture Slider Section */}
      <section>
        <GenericProductSlider
          products={catFurniture}
          title="Cat Furniture"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>

      {/* Specialty Furniture Slider Section */}
      <section className="mb-10">
        <GenericProductSlider
          products={specialtyFurniture}
          title="Specialty Furniture"
          slidesToShow={4}
          autoplay={false}
          className="bg-gray-50 py-10 rounded-xl"
        />
      </section>
    </>
  )
}

export default Furniture
