import React from 'react'
import { Fa<PERSON>nstagram, FaTwitter, FaFacebook, FaLinkedin } from 'react-icons/fa'
import GenericProductSlider from '../Slider/GenericProductSlider'

const Food = () => {
  // Product data for dog food
  const dogFood = [
    {
      id: 1,
      title: 'Premium Dry Dog Food',
      img: 'https://www.pngplay.com/wp-content/uploads/6/Drools-Dog-Food-Transparent-PNG.png',
      price: '$49.99',
      description: 'High-quality dry food for adult dogs'
    },
    {
      id: 2,
      title: 'Puppy Formula',
      img: 'https://wallpapers.com/images/hd/gourmet-dog-food-png-unt-2gigx2pk2bcs5hv2.png',
      price: '$54.99',
      description: 'Specially formulated for growing puppies'
    },
    {
      id: 3,
      title: 'Senior Dog Food',
      img: 'https://pngdow.com/public/uploads/thumbnail/dog-food-png-image-with-transparent-background-for-free-dog-food-4-11688836734cuufefecnv.png',
      price: '$52.99',
      description: 'Formulated for senior dogs'
    },
    {
      id: 4,
      title: 'Grain-Free Dog Food',
      img: 'https://wallpapers.com/images/hd/pedigree-dog-food-lamb-rice-flavor-package-9shrk3548vuon0n7.png',
      price: '$59.99',
      description: 'Grain-free formula for sensitive dogs'
    },
    {
      id: 5,
      title: 'Wet Dog Food',
      img: 'https://wallpapers.com/images/hd/air-dried-dog-food-png-05252024-0175lm63mctwk3t1.png',
      price: '$24.99',
      description: 'Premium wet food for all breeds'
    }
  ];

  // Product data for cat food
  const catFood = [
    {
      id: 101,
      title: 'Indoor Cat Formula',
      img: 'https://i5.walmartimages.com/asr/79bfdf3b-d48d-42f0-90d0-ac297855cae9_3.3926583857bb0301951b5019a994f582.png',
      price: '$39.99',
      description: 'Specially formulated for indoor cats'
    },
    {
      id: 102,
      title: 'Kitten Food',
      img: 'https://i5.walmartimages.com/asr/f413ea66-ec7c-4582-ab00-e526decdd200_3.03718d2e9c3df95295bd4f5fad91e1a6.png',
      price: '$42.99',
      description: 'Nutrient-rich food for growing kittens'
    },
    {
      id: 103,
      title: 'Senior Cat Food',
      img: 'https://content.syndigo.com/asset/f3e5841e-8b4b-4800-933b-73aeb0cf2fd5/960.png',
      price: '$44.99',
      description: 'Easy-to-digest formula for senior cats'
    },
    {
      id: 104,
      title: 'Wet Cat Food',
      img: 'https://1.bp.blogspot.com/-OIpl3PFqXvY/UqXrcnyahWI/AAAAAAAACco/yx4u4wAUGhA/s1600/Bili+Ka+Khana.png',
      price: '$22.99',
      description: 'Premium wet food for all cats'
    },
    {
      id: 105,
      title: 'Grain-Free Cat Food',
      img: 'http://www.petsense.com/cdn/shop/files/888641133765_1_grande.png?v=1710869984',
      price: '$47.99',
      description: 'Grain-free formula for sensitive cats'
    }
  ];

  // Product data for specialty food
  const specialtyFood = [
    {
      id: 201,
      title: 'Weight Management',
      img: 'https://fastpng.com/images/file/dog-food-png-847-x-861-oy86sgw8w11gljm4.png',
      price: '$54.99',
      description: 'For pets that need weight control'
    },
    {
      id: 202,
      title: 'Sensitive Stomach',
      img: 'https://diamondpet.storage.googleapis.com/wp-content/uploads/2023/07/12102334/image6.png',
      price: '$56.99',
      description: 'Easy to digest for sensitive pets'
    },
    {
      id: 203,
      title: 'Allergy Formula',
      img: 'https://www.masterfeeds.com/wp-content/uploads/NewPetFoodBags.png',
      price: '$59.99',
      description: 'For pets with food allergies'
    },
    {
      id: 204,
      title: 'Organic Pet Food',
      img: 'http://www.havesippywilltravel.com/wp-content/uploads/2015/04/pic81.png',
      price: '$64.99',
      description: 'Made with organic ingredients'
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section>
        <div className='p-10'>
          <div className='grid grid-cols-2 w-full rounded-4xl p-10 relative'>
            <div className='flex justify-center'>
              <img className='h-130 object-contain' src='https://www.oasy.com/media/immagini/4093_n_pack-home.png' alt="pet food" />
            </div>

            <div className='flex flex-col justify-center'>
              <h1 className='text-6xl font-bold'>
                Premium Pet Food
              </h1>

              <div className='pt-10'>
                <p className='block text-xl font-normal mt-2'>
                  Nutritious and delicious food options for your pets. We offer high-quality food for dogs and cats of all ages and dietary needs.
                </p>

                <div className='flex items-center gap-2 mt-4'>
                  <div className='h-px bg-gray-400 flex-grow'></div>
                  <div className='flex gap-3'>
                    <a href="https://www.instagram.com" target='_blank'><FaInstagram className="text-[#575CEE] text-xl" /></a>
                    <a href="https://twitter.com" target='_blank'><FaTwitter className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.facebook.com" target='_blank'><FaFacebook className="text-[#575CEE] text-xl" /></a>
                    <a href="https://www.linkedin.com" target='_blank'><FaLinkedin className="text-[#575CEE] text-xl" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dog Food Slider Section */}
      <section>
        <GenericProductSlider
          products={dogFood}
          title="Dog Food"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>

      {/* Cat Food Slider Section */}
      <section>
        <GenericProductSlider
          products={catFood}
          title="Cat Food"
          slidesToShow={3}
          autoplaySpeed={4000}
        />
      </section>

      {/* Specialty Food Slider Section */}
      <section className="mb-10">
        <GenericProductSlider
          products={specialtyFood}
          title="Specialty Food"
          slidesToShow={4}
          autoplay={false}
          className="bg-gray-50 py-10 rounded-xl"
        />
      </section>
    </>
  )
}

export default Food
